package helper

import (
	"strings"

	"github.com/spf13/cast"

	"hotel/common/utils"
)

func NewLogData(prefix string) *LogData {
	return &LogData{prefix: prefix}
}

// LogData not thread-safe;
type LogData struct {
	prefix string
	kvs    []KV
}

func (ld *LogData) AppendIfNonEmpty(k string, v interface{}) *LogData {
	if utils.IsZero(v) {
		return ld
	}
	return ld.Append(k, v)
}

func (ld *LogData) AppendIfNonNil(k string, v interface{}) *LogData {
	if v == nil {
		return ld
	}
	ld.Append(k, v)
	return ld
}
func (ld *LogData) Append(k string, v interface{}) *LogData {
	ld.kvs = append(ld.kvs, KV{k, v})
	return ld
}
func (ld *LogData) ToMap() map[string]interface{} {
	out := make(map[string]interface{})
	for _, item := range ld.kvs {
		out[item.Key] = item.Val
	}
	return out
}

func (ld *LogData) Build() string {
	sb := strings.Builder{}
	sb.WriteString(ld.prefix)
	sb.WriteString(":")
	for _, item := range ld.kvs {
		sb.WriteString(" ")
		sb.WriteString(item.Key)
		sb.WriteString("(")
		sb.WriteString(cast.ToString(item.Val)) // 结构体没实现string\error接口的打印不出来
		sb.WriteString(")")
	}
	return sb.String()
}

type KV struct {
	Key string
	Val interface{}
}
