# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Backend (Go)
```bash
# Development workflow
make setup-dev          # First-time setup (only run once)
make start-infra         # Start MySQL, Redis services via Docker
make run-dev            # Run application in development mode
make stop-infra         # Stop infrastructure services

# Testing and quality
make test               # Run all tests
make test-package PKG=content/mysql  # Run tests for specific package
make test-coverage      # Run tests with coverage report
make lint               # Run golangci-lint

# Build and deployment
make build              # Build the application
make dao                # Generate API docs and DAO models
make ddl                # Generate database DDL
make doc                # Update API documentation

# Utility commands
make status-infra       # Check infrastructure services status
make clean              # Clean build artifacts
make help               # Show all available commands
```

### Frontend (Vue 3 + TypeScript)
```bash
cd admin-fe
pnpm install            # Install dependencies
pnpm dev                # Development server (port 3008)
pnpm build              # Production build
pnpm build:uat         # UAT build
pnpm lint               # ESLint
pnpm fix                # Fix linting issues

# End-to-end testing
pnpm test:e2e          # Run Playwright tests
pnpm test:e2e:ui       # Run tests with UI
pnpm test:e2e:debug    # Debug tests
```

## Architecture Overview

This is a hotel API distribution platform with microservices architecture built in Go, supporting multi-supplier integration and full hotel booking lifecycle management.

### Core Modules

- **api/**: Main API gateway and HTTP service layer using go-zero framework
- **user/**: User management, authentication, entity management, role-based access control
- **search/**: Hotel search engine with geography-based queries and price caching
- **supplier/**: Multi-supplier integration (Ctrip, Dida, DerbySoft, HotelBeds, Expedia, etc.)
- **trade/**: Complete order lifecycle management, booking, cancellation, rebooking
- **content/**: Hotel content management, room types, facility information
- **geography/**: Geospatial services, location search with Bleve full-text search
- **rule/**: Business rules engine for pricing and booking policies
- **mapping/**: Hotel and room type mapping between suppliers
- **common/**: Shared utilities, logging, CQRS, i18n, error handling

### Database Architecture

**Physical Databases:**
- `item`: Search, supplier, mapping, geography, content, simulator services  
- `user`: User, rule, trade, API gateway services

**Key Technologies:**
- **Framework**: go-zero (REST), Hertz (HTTP client)
- **Database**: MySQL with spatial indexing, Redis for caching
- **Search**: Bleve full-text search engine
- **Message Queue**: Redis Streams, NSQ
- **Frontend**: Vue 3 + TypeScript + Element Plus
- **Testing**: Playwright for E2E, Go standard testing for unit tests

### Supplier Integration Pattern

Each supplier (ctrip/, dida/, derbysoft/, etc.) follows a consistent pattern:
- `converter.go`: Request/response transformation
- `service.go`: Core business logic implementation  
- `init.go`: Service initialization and configuration
- `*_test.go`: Integration and unit tests

### Project-Specific Guidelines

#### Code Organization
- Use nested directory structure organized by menu hierarchy
- Prefer shorter path names (e.g., 'order' instead of 'order-management')
- All services follow the pattern: domain/ → mysql/ → protocol/ → service/

#### API Integration
- Frontend axios interceptor returns `response.data` directly
- Use string type for large integer IDs to avoid JavaScript precision issues
- Include sessionId in headers for checkAvail API calls
- Cache InternalAdminHomepage API response for 10+ minutes

#### Testing Strategy
- Unit test package names match the tested package (e.g., `content/mysql/test`)
- E2E tests located in `admin-fe/tests/e2e/`
- Prefer real implementations over mocks, especially for complex business logic
- Follow TDD: write tests → implement → iterate

#### Multilingual Support
- Backend delivers i18n content via `InternalHomepage` method
- Use `i18n.I18N` objects instead of simple strings
- API-delivered content has higher priority than frontend starlingMap
- Replace hardcoded language fields with extensible i18n patterns

#### Development Best Practices
- Dependency injection at service initialization (fail-fast approach)
- Use `hotel/common/log` with context for all logging
- Avoid anonymous structs for maintainability
- Use pointer receivers when modifying struct properties
- Follow existing CORS header management patterns in `common/protocol/header.go`

#### Performance Considerations
- Spatial indexing with boundary box pre-filtering for geography queries
- FULLTEXT indexes for hotel name searches with fallback strategies
- Batch size optimization for bulk operations
- Circuit breaker patterns for external supplier calls

## Important Files to Check

- `Makefile`: All development commands and workflows
- `.cursorrules`: Project-specific coding standards and requirements
- `build/build.sh`: Main build script and DAO generation
- `api/api.go`: Main application entry point
- `common/protocol/header.go`: CORS and header management
- `admin-fe/package.json`: Frontend dependencies and scripts