# HotelRates 接口演示 - Link Technology 架构

本演示展示了酒店预订系统中 HotelRates 接口的完整实现，重点展示 Link Technology 架构中如何通过规则引擎（使用 `github.com/bytedance/arishem` 库）动态修改请求和响应对象。

## 架构概述

Link Technology 是一个基于规则引擎的微服务架构，用于处理多供应商的酒店预订业务。核心思想是通过规则引擎在请求和响应的不同阶段应用业务规则，实现灵活的业务逻辑控制。

### 核心组件

1. **买家解析器 (BuyerResolver)**: 负责获取可用的供应商列表
2. **参与者 (Participant)**: 包含入节点和出节点，每个节点可以配置规则
3. **规则引擎**: 基于 arishem 库，支持复杂的条件判断和动作执行
4. **供应商工厂**: 统一管理不同供应商的接口调用

## 处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant BuyerIn as 买家入节点
    participant SellerIn as 卖家入节点
    participant Supplier as 供应商
    participant SellerOut as 卖家出节点
    participant BuyerOut as 买家出节点

    Client->>BuyerIn: 发送酒店查询请求
    BuyerIn->>BuyerIn: 应用买家前置规则
    BuyerIn->>SellerIn: 传递卖家凭证
    SellerIn->>SellerIn: 应用卖家前置规则
    SellerIn->>Supplier: 调用供应商接口
    Supplier->>SellerOut: 返回原始响应
    SellerOut->>SellerOut: 应用卖家响应规则
    SellerOut->>BuyerOut: 统一响应格式
    BuyerOut->>BuyerOut: 应用买家后置规则
    BuyerOut->>Client: 返回最终结果
```

## 文件结构

```
demo/
├── hotel_rates_demo.go      # 主要的演示程序
├── link_technology_demo.go  # Link Technology 架构演示
└── README.md               # 说明文档
```

## 运行方法

### 1. 运行规则引擎演示

```bash
cd demo
go run hotel_rates_demo.go
```

### 2. 运行 Link Technology 架构演示

```bash
cd demo
go run link_technology_demo.go
```

## 规则示例

### 1. 基础条件判断

```json
{
    "OpLogic": "&&",
    "Conditions": [
        {
            "Operator": ">=",
            "Lhs": {
                "VarExpr": "user.age"
            },
            "Rhs": {
                "Const": {
                    "NumConst": 18
                }
            }
        }
    ]
}
```

### 2. 请求参数修改规则

```json
{
    "ActionList": [
        {
            "ActionName": "request",
            "Params": {
                "supplierHotelId": "TBO_12345",
                "regionName": "北京",
                "supplierCityId": "PEK"
            }
        }
    ]
}
```

### 3. 响应数据修改规则

```json
{
    "Expr": {
        "FuncExpr": {
            "FuncName": "modifyResponse",
            "Args": [
                {
                    "VarExpr": "supplierResp"
                },
                {
                    "Const": {
                        "NumConst": 1.1
                    }
                }
            ]
        }
    }
}
```

## 核心特性

### 1. 动态请求修改

- 根据供应商类型设置不同的请求参数
- 支持语言、货币等上下文信息的注入
- 可以动态调整 API 密钥和认证信息

### 2. 动态响应处理

- 支持价格策略的动态调整
- 可以应用不同的加价策略
- 支持 VIP 折扣和特殊服务

### 3. 并发处理

- 同时请求多个供应商
- 使用 errgroup 管理并发
- 线程安全的结果合并

### 4. 错误处理

- 供应商接口异常处理
- 规则引擎执行异常处理
- 优雅降级机制

## 配置说明

### 买家解析器配置

```go
buyerResolver := &DemoBuyerResolver{
    buyer: linktech.Participant{
        InNode:  &DemoInNode{ruleId: 1, credentialId: 100},
        OutNode: &DemoOutNode{ruleId: 2},
    },
    sellers: []linktech.Participant{
        {
            InNode:  &DemoInNode{ruleId: 3, credentialId: 101},
            OutNode: &DemoOutNode{ruleId: 4},
        },
        {
            InNode:  &DemoInNode{ruleId: 5, credentialId: 102},
            OutNode: &DemoOutNode{ruleId: 6},
        },
    },
}
```

### 规则配置

每个节点可以配置不同的规则：

- **入节点规则**: 用于修改请求参数和上下文
- **出节点规则**: 用于修改响应数据和业务逻辑

## 扩展指南

### 1. 添加新的供应商

1. 实现 `Supplier` 接口
2. 在工厂中注册新的供应商
3. 配置相应的规则

### 2. 添加新的规则类型

1. 定义规则条件表达式
2. 定义规则动作
3. 在相应的节点中应用规则

### 3. 添加新的业务逻辑

1. 在规则引擎中定义新的函数
2. 在规则中使用新的函数
3. 更新相应的处理逻辑

## 技术栈

- **Go**: 主要开发语言
- **Arishem**: 规则引擎库
- **Link Technology**: 微服务架构模式
- **Errgroup**: 并发控制
- **Context**: 上下文管理

## 总结

本演示展示了如何通过 Link Technology 架构和规则引擎实现灵活的酒店预订系统。通过规则引擎，我们可以：

1. 动态修改请求和响应
2. 支持多供应商并发处理
3. 实现复杂的业务逻辑
4. 提供良好的扩展性

这种架构特别适合需要处理多个供应商、复杂业务规则和动态配置的场景。 