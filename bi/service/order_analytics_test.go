package service

import (
	"context"
	"testing"
	"time"

	"hotel/bi/mysql"
	"hotel/bi/protocol"
	"hotel/common/money"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockOrderAnalyticsDAO 模拟DAO
type MockOrderAnalyticsDAO struct {
	mock.Mock
}

func (m *MockOrderAnalyticsDAO) GetOrderOverview(ctx context.Context, startDate, endDate time.Time, entityId *int64) (*mysql.OrderMetrics, error) {
	args := m.Called(ctx, startDate, endDate, entityId)
	return args.Get(0).(*mysql.OrderMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetDailyTrend(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*mysql.OrderMetrics, error) {
	args := m.Called(ctx, startDate, endDate, entityId)
	return args.Get(0).([]*mysql.OrderMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetStatusBreakdown(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*mysql.StatusMetrics, error) {
	args := m.Called(ctx, startDate, endDate, entityId)
	return args.Get(0).([]*mysql.StatusMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetRevenueByCurrency(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*mysql.StatusMetrics, error) {
	args := m.Called(ctx, startDate, endDate, entityId)
	return args.Get(0).([]*mysql.StatusMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetRevenueByEntity(ctx context.Context, startDate, endDate time.Time) ([]*mysql.EntityMetrics, error) {
	args := m.Called(ctx, startDate, endDate)
	return args.Get(0).([]*mysql.EntityMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetEntityPerformance(ctx context.Context, startDate, endDate time.Time, limit int64) ([]*mysql.EntityMetrics, error) {
	args := m.Called(ctx, startDate, endDate, limit)
	return args.Get(0).([]*mysql.EntityMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetTopPerformingDays(ctx context.Context, startDate, endDate time.Time, limit int64) ([]*mysql.OrderMetrics, error) {
	args := m.Called(ctx, startDate, endDate, limit)
	return args.Get(0).([]*mysql.OrderMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetMonthlyComparison(ctx context.Context, months int) ([]*mysql.OrderMetrics, error) {
	args := m.Called(ctx, months)
	return args.Get(0).([]*mysql.OrderMetrics), args.Error(1)
}

func (m *MockOrderAnalyticsDAO) GetRealTimeMetrics(ctx context.Context) (*mysql.OrderMetrics, error) {
	args := m.Called(ctx)
	return args.Get(0).(*mysql.OrderMetrics), args.Error(1)
}

func TestOrderAnalyticsService_GetOrderAnalytics(t *testing.T) {
	ctx := context.Background()
	mockDAO := new(MockOrderAnalyticsDAO)
	service := NewOrderAnalyticsService(mockDAO)

	// 准备测试数据
	startDate := time.Now().AddDate(0, -1, 0)
	endDate := time.Now()

	// 模拟总览数据
	mockOverview := &mysql.OrderMetrics{
		Date:              time.Now(),
		TotalOrders:       100,
		TotalRevenue:      1000000,
		TotalCost:         800000,
		TotalProfit:       200000,
		CompletedOrders:   80,
		CancelledOrders:   10,
		PendingOrders:     10,
		RevenueCurrency:   "USD",
	}

	// 模拟趋势数据
	mockTrendData := []*mysql.OrderMetrics{
		{
			Date:              startDate,
			TotalOrders:       10,
			TotalRevenue:      100000,
			TotalProfit:       20000,
			CompletedOrders:   8,
			CancelledOrders:   1,
			RevenueCurrency:   "USD",
		},
		{
			Date:              startDate.AddDate(0, 0, 1),
			TotalOrders:       15,
			TotalRevenue:      150000,
			TotalProfit:       30000,
			CompletedOrders:   12,
			CancelledOrders:   2,
			RevenueCurrency:   "USD",
		},
	}

	// 模拟状态数据
	mockStatusData := []*mysql.StatusMetrics{
		{Status: 1, Count: 20, Revenue: 200000, Currency: "USD"},
		{Status: 2, Count: 30, Revenue: 300000, Currency: "USD"},
		{Status: 5, Count: 40, Revenue: 400000, Currency: "USD"},
		{Status: 6, Count: 10, Revenue: 100000, Currency: "USD"},
	}

	// 模拟币种数据
	mockCurrencyData := []*mysql.StatusMetrics{
		{Status: 0, Count: 60, Revenue: 600000, Currency: "USD"},
		{Status: 0, Count: 30, Revenue: 300000, Currency: "EUR"},
		{Status: 0, Count: 10, Revenue: 100000, Currency: "CNY"},
	}

	// 模拟实体数据
	mockEntityData := []*mysql.EntityMetrics{
		{EntityId: 1, EntityName: "Entity_1", OrderCount: 50, Revenue: 500000, Profit: 100000, Currency: "USD"},
		{EntityId: 2, EntityName: "Entity_2", OrderCount: 30, Revenue: 300000, Profit: 60000, Currency: "USD"},
		{EntityId: 3, EntityName: "Entity_3", OrderCount: 20, Revenue: 200000, Profit: 40000, Currency: "USD"},
	}

	// 模拟月度数据
	mockMonthlyData := []*mysql.OrderMetrics{
		{
			Date:              time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			TotalOrders:       80,
			TotalRevenue:      800000,
			TotalProfit:       160000,
			RevenueCurrency:   "USD",
		},
		{
			Date:              time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
			TotalOrders:       100,
			TotalRevenue:      1000000,
			TotalProfit:       200000,
			RevenueCurrency:   "USD",
		},
	}

	// 设置mock期望
	mockDAO.On("GetOrderOverview", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), (*int64)(nil)).Return(mockOverview, nil)
	mockDAO.On("GetOrderOverview", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), (*int64)(nil)).Return(mockOverview, nil) // 用于增长率计算
	mockDAO.On("GetDailyTrend", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), (*int64)(nil)).Return(mockTrendData, nil)
	mockDAO.On("GetStatusBreakdown", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), (*int64)(nil)).Return(mockStatusData, nil)
	mockDAO.On("GetRevenueByCurrency", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), (*int64)(nil)).Return(mockCurrencyData, nil)
	mockDAO.On("GetRevenueByEntity", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time")).Return(mockEntityData, nil)
	mockDAO.On("GetMonthlyComparison", ctx, 12).Return(mockMonthlyData, nil)
	mockDAO.On("GetEntityPerformance", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), int64(10)).Return(mockEntityData, nil)
	mockDAO.On("GetTopPerformingDays", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), int64(10)).Return(mockTrendData, nil)

	// 执行测试
	req := &protocol.OrderAnalyticsReq{
		StartDate:   &startDate,
		EndDate:     &endDate,
		Granularity: "day",
	}

	resp, err := service.GetOrderAnalytics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.NotNil(t, resp.Overview)
	assert.Equal(t, int64(100), resp.Overview.TotalOrders)
	assert.Equal(t, int64(1000000), resp.Overview.TotalRevenue.Amount)
	assert.Equal(t, "USD", resp.Overview.TotalRevenue.Currency)
	assert.Equal(t, int64(200000), resp.Overview.TotalProfit.Amount)
	assert.Equal(t, float64(80), resp.Overview.CompletionRate)
	assert.Equal(t, float64(10), resp.Overview.CancellationRate)

	// 验证趋势数据
	assert.Len(t, resp.TrendData, 2)
	assert.Equal(t, int64(10), resp.TrendData[0].OrderCount)
	assert.Equal(t, int64(100000), resp.TrendData[0].Revenue.Amount)

	// 验证状态分布
	assert.Len(t, resp.StatusBreakdown, 4)
	assert.Equal(t, int64(1), resp.StatusBreakdown[0].Status)
	assert.Equal(t, int64(20), resp.StatusBreakdown[0].Count)

	// 验证收入分析
	assert.NotNil(t, resp.RevenueAnalysis)
	assert.Equal(t, int64(1000000), resp.RevenueAnalysis.TotalRevenue.Amount)
	assert.Equal(t, int64(200000), resp.RevenueAnalysis.TotalProfit.Amount)
	assert.Equal(t, float64(20), resp.RevenueAnalysis.ProfitMargin)

	// 验证表现排行
	assert.NotNil(t, resp.TopPerformers)
	assert.Len(t, resp.TopPerformers.TopEntities, 3)
	assert.Equal(t, int64(1), resp.TopPerformers.TopEntities[0].EntityId)

	// 验证所有mock调用
	mockDAO.AssertExpectations(t)
}

func TestOrderAnalyticsService_GetRealTimeMetrics(t *testing.T) {
	ctx := context.Background()
	mockDAO := new(MockOrderAnalyticsDAO)
	service := NewOrderAnalyticsService(mockDAO)

	// 准备测试数据
	mockMetrics := &mysql.OrderMetrics{
		Date:              time.Now(),
		TotalOrders:       50,
		TotalRevenue:      500000,
		TotalCost:        400000,
		TotalProfit:      100000,
		CompletedOrders:   40,
		CancelledOrders:   5,
		PendingOrders:     5,
		RevenueCurrency:   "USD",
	}

	// 设置mock期望
	mockDAO.On("GetRealTimeMetrics", ctx).Return(mockMetrics, nil)

	// 执行测试
	req := &protocol.RealTimeMetricsReq{
		Metrics: []string{"todayOrders", "todayRevenue", "completionRate"},
	}

	resp, err := service.GetRealTimeMetrics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int64(50), resp.TodayOrders)
	assert.Equal(t, int64(500000), resp.TodayRevenue.Amount)
	assert.Equal(t, "USD", resp.TodayRevenue.Currency)
	assert.Equal(t, int64(40), resp.ActiveBookings)
	assert.Equal(t, int64(5), resp.PendingOrders)
	assert.Equal(t, float64(80), resp.CompletionRate)
	assert.Equal(t, float64(10), resp.CancellationRate)

	// 验证mock调用
	mockDAO.AssertExpectations(t)
}

func TestOrderAnalyticsService_CalculateGrowthRate(t *testing.T) {
	ctx := context.Background()
	mockDAO := new(MockOrderAnalyticsDAO)
	service := NewOrderAnalyticsService(mockDAO)

	// 准备测试数据
	startDate := time.Now().AddDate(0, -1, 0)
	endDate := time.Now()

	currentMetrics := &mysql.OrderMetrics{
		TotalRevenue: 1000000,
	}

	prevMetrics := &mysql.OrderMetrics{
		TotalRevenue: 800000,
	}

	// 设置mock期望
	mockDAO.On("GetOrderOverview", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), (*int64)(nil)).Return(currentMetrics, nil).Once()
	mockDAO.On("GetOrderOverview", ctx, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time"), (*int64)(nil)).Return(prevMetrics, nil).Once()

	// 执行测试
	growthRate, err := service.calculateGrowthRate(ctx, startDate, endDate, nil)

	// 断言
	assert.NoError(t, err)
	assert.Equal(t, float64(25), growthRate) // (1000000 - 800000) / 800000 * 100 = 25%
}

func TestOrderAnalyticsService_GetStatusName(t *testing.T) {
	service := &OrderAnalyticsService{}

	tests := []struct {
		status   int64
		expected string
	}{
		{1, "已创建"},
		{2, "已支付"},
		{3, "等待确认"},
		{4, "已确认"},
		{5, "已完成"},
		{6, "已取消"},
		{7, "等待取消"},
		{8, "等待退款"},
		{999, "状态_999"},
	}

	for _, tt := range tests {
		result := service.getStatusName(tt.status)
		assert.Equal(t, tt.expected, result["zh-CN"])
	}
}