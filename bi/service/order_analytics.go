package service

import (
	"context"
	"fmt"
	"time"

	"hotel/bi/mysql"
	"hotel/bi/protocol"
	"hotel/common/i18n"
	"hotel/common/log"
	"hotel/common/money"
)

// OrderAnalyticsService 订单分析服务
type OrderAnalyticsService struct {
	orderDAO *mysql.OrderAnalyticsDAO
}

// NewOrderAnalyticsService 创建订单分析服务
func NewOrderAnalyticsService(orderDAO *mysql.OrderAnalyticsDAO) *OrderAnalyticsService {
	return &OrderAnalyticsService{
		orderDAO: orderDAO,
	}
}

// GetOrderAnalytics 获取订单分析数据
func (s *OrderAnalyticsService) GetOrderAnalytics(ctx context.Context, req *protocol.OrderAnalyticsReq) (*protocol.OrderAnalyticsResp, error) {
	// 设置默认时间范围
	startDate := req.StartDate
	endDate := req.EndDate
	if startDate == nil {
		t := time.Now().AddDate(0, -1, 0) // 默认最近一个月
		startDate = &t
	}
	if endDate == nil {
		t := time.Now()
		endDate = &t
	}

	// 获取总览数据
	overview, err := s.getOrderOverview(ctx, *startDate, *endDate, req.EntityId)
	if err != nil {
		log.Errorc(ctx, "Failed to get order overview: %v", err)
		return nil, fmt.Errorf("failed to get order overview: %w", err)
	}

	// 获取趋势数据
	trendData, err := s.getOrderTrendData(ctx, *startDate, *endDate, req.Granularity, req.EntityId)
	if err != nil {
		log.Errorc(ctx, "Failed to get trend data: %v", err)
		return nil, fmt.Errorf("failed to get trend data: %w", err)
	}

	// 获取状态分布
	statusBreakdown, err := s.getStatusBreakdown(ctx, *startDate, *endDate, req.EntityId)
	if err != nil {
		log.Errorc(ctx, "Failed to get status breakdown: %v", err)
		return nil, fmt.Errorf("failed to get status breakdown: %w", err)
	}

	// 获取收入分析
	revenueAnalysis, err := s.getRevenueAnalysis(ctx, *startDate, *endDate, req.EntityId)
	if err != nil {
		log.Errorc(ctx, "Failed to get revenue analysis: %v", err)
		return nil, fmt.Errorf("failed to get revenue analysis: %w", err)
	}

	// 获取表现排行
	topPerformers, err := s.getTopPerformers(ctx, *startDate, *endDate)
	if err != nil {
		log.Errorc(ctx, "Failed to get top performers: %v", err)
		return nil, fmt.Errorf("failed to get top performers: %w", err)
	}

	return &protocol.OrderAnalyticsResp{
		Overview:        overview,
		TrendData:       trendData,
		StatusBreakdown: statusBreakdown,
		RevenueAnalysis: revenueAnalysis,
		TopPerformers:   topPerformers,
	}, nil
}

// getOrderOverview 获取订单总览
func (s *OrderAnalyticsService) getOrderOverview(ctx context.Context, startDate, endDate time.Time, entityId *int64) (*protocol.OrderOverview, error) {
	metrics, err := s.orderDAO.GetOrderOverview(ctx, startDate, endDate, entityId)
	if err != nil {
		return nil, err
	}

	totalOrders := metrics.TotalOrders
	if totalOrders == 0 {
		totalOrders = 1 // 避免除零错误
	}

	cancellationRate := float64(metrics.CancelledOrders) / float64(totalOrders) * 100
	completionRate := float64(metrics.CompletedOrders) / float64(totalOrders) * 100
	bookingRate := completionRate // 简化处理，实际应该是预订转化率

	avgOrderValue := money.Money{
		Amount:   float64(metrics.TotalRevenue) / float64(totalOrders),
		Currency: metrics.RevenueCurrency,
	}

	// 计算增长率（需要对比上期数据）
	growthRate, err := s.calculateGrowthRate(ctx, startDate, endDate, entityId)
	if err != nil {
		log.Warnc(ctx, "Failed to calculate growth rate: %v", err)
		growthRate = 0
	}

	return &protocol.OrderOverview{
		TotalOrders: metrics.TotalOrders,
		TotalRevenue: money.Money{
			Amount:   float64(metrics.TotalRevenue),
			Currency: metrics.RevenueCurrency,
		},
		TotalProfit: money.Money{
			Amount:   float64(metrics.TotalProfit),
			Currency: "USD",
		},
		AverageOrderValue: avgOrderValue,
		BookingRate:       bookingRate,
		CancellationRate:  cancellationRate,
		CompletionRate:    completionRate,
		GrowthRate:        growthRate,
	}, nil
}

// getOrderTrendData 获取趋势数据
func (s *OrderAnalyticsService) getOrderTrendData(ctx context.Context, startDate, endDate time.Time, granularity string, entityId *int64) ([]*protocol.OrderTrendData, error) {
	if granularity == "" {
		granularity = "day"
	}

	var metrics []*mysql.OrderMetrics
	var err error

	if granularity == "day" {
		metrics, err = s.orderDAO.GetDailyTrend(ctx, startDate, endDate, entityId)
	} else {
		metrics, err = s.orderDAO.GetOrderMetricsByDateRange(ctx, startDate, endDate, granularity)
	}

	if err != nil {
		return nil, err
	}

	var trendData []*protocol.OrderTrendData
	for _, metric := range metrics {
		trendData = append(trendData, &protocol.OrderTrendData{
			Date:       metric.Date,
			OrderCount: metric.TotalOrders,
			Revenue: money.Money{
				Amount:   float64(metric.TotalRevenue),
				Currency: metric.RevenueCurrency,
			},
			Profit: money.Money{
				Amount:   float64(metric.TotalProfit),
				Currency: "USD",
			},
			BookingCount: metric.CompletedOrders,
			CancelCount:  metric.CancelledOrders,
		})
	}

	return trendData, nil
}

// getStatusBreakdown 获取状态分布
func (s *OrderAnalyticsService) getStatusBreakdown(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*protocol.OrderStatusStat, error) {
	metrics, err := s.orderDAO.GetStatusBreakdown(ctx, startDate, endDate, entityId)
	if err != nil {
		return nil, err
	}

	// 计算总订单数用于计算占比
	var totalOrders int64
	for _, metric := range metrics {
		totalOrders += metric.Count
	}

	if totalOrders == 0 {
		totalOrders = 1
	}

	var statusStats []*protocol.OrderStatusStat
	for _, metric := range metrics {
		statusName := s.getStatusName(metric.Status)
		percentage := float64(metric.Count) / float64(totalOrders) * 100

		statusStats = append(statusStats, &protocol.OrderStatusStat{
			Status:     metric.Status,
			StatusName: statusName,
			Count:      metric.Count,
			Percentage: percentage,
			Revenue: money.Money{
				Amount:   float64(metric.Revenue),
				Currency: metric.Currency,
			},
		})
	}

	return statusStats, nil
}

// getRevenueAnalysis 获取收入分析
func (s *OrderAnalyticsService) getRevenueAnalysis(ctx context.Context, startDate, endDate time.Time, entityId *int64) (*protocol.RevenueAnalysis, error) {
	// 获取总体收入数据
	overview, err := s.orderDAO.GetOrderOverview(ctx, startDate, endDate, entityId)
	if err != nil {
		return nil, err
	}

	// 获取按币种分组的收入
	currencyMetrics, err := s.orderDAO.GetRevenueByCurrency(ctx, startDate, endDate, entityId)
	if err != nil {
		return nil, err
	}

	var revenueByCurrency []*protocol.CurrencyRevenue
	totalRevenue := overview.TotalRevenue
	if totalRevenue == 0 {
		totalRevenue = 1
	}

	for _, metric := range currencyMetrics {
		percentage := float64(metric.Revenue) / float64(totalRevenue) * 100
		revenueByCurrency = append(revenueByCurrency, &protocol.CurrencyRevenue{
			Currency: metric.Currency,
			Revenue: money.Money{
				Amount:   float64(metric.Revenue),
				Currency: metric.Currency,
			},
			OrderCount: metric.Count,
			Percentage: percentage,
		})
	}

	// 获取按实体分组的收入
	entityMetrics, err := s.orderDAO.GetRevenueByEntity(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	var revenueByEntity []*protocol.EntityRevenue
	for _, metric := range entityMetrics {
		entityName := i18n.I18N{
			Zh: fmt.Sprintf("实体_%d", metric.EntityId),
			En: fmt.Sprintf("Entity_%d", metric.EntityId),
		}

		revenueByEntity = append(revenueByEntity, &protocol.EntityRevenue{
			EntityId:   metric.EntityId,
			EntityName: entityName,
			Revenue: money.Money{
				Amount:   float64(metric.Revenue),
				Currency: metric.Currency,
			},
			Profit: money.Money{
				Amount:   float64(metric.Profit),
				Currency: "USD",
			},
			OrderCount: metric.OrderCount,
		})
	}

	// 获取月度对比数据
	monthlyMetrics, err := s.orderDAO.GetMonthlyComparison(ctx, 12)
	if err != nil {
		return nil, err
	}

	var monthlyComparison []*protocol.MonthlyComparison
	for i, metric := range monthlyMetrics {
		var growthRate float64
		if i > 0 {
			prevRevenue := monthlyMetrics[i-1].TotalRevenue
			if prevRevenue > 0 {
				growthRate = float64(metric.TotalRevenue-prevRevenue) / float64(prevRevenue) * 100
			}
		}

		monthlyComparison = append(monthlyComparison, &protocol.MonthlyComparison{
			Month: metric.Date,
			Revenue: money.Money{
				Amount:   float64(metric.TotalRevenue),
				Currency: metric.RevenueCurrency,
			},
			Profit: money.Money{
				Amount:   float64(metric.TotalProfit),
				Currency: "USD",
			},
			OrderCount: metric.TotalOrders,
			GrowthRate: growthRate,
		})
	}

	profitMargin := float64(0)
	if overview.TotalRevenue > 0 {
		profitMargin = float64(overview.TotalProfit) / float64(overview.TotalRevenue) * 100
	}

	return &protocol.RevenueAnalysis{
		TotalRevenue: money.Money{
			Amount:   float64(overview.TotalRevenue),
			Currency: overview.RevenueCurrency,
		},
		TotalCost: money.Money{
			Amount:   float64(overview.TotalCost),
			Currency: overview.RevenueCurrency,
		},
		TotalProfit: money.Money{
			Amount:   float64(overview.TotalProfit),
			Currency: "USD",
		},
		ProfitMargin:      profitMargin,
		RevenueByCurrency: revenueByCurrency,
		RevenueByEntity:   revenueByEntity,
		MonthlyComparison: monthlyComparison,
	}, nil
}

// getTopPerformers 获取表现排行
func (s *OrderAnalyticsService) getTopPerformers(ctx context.Context, startDate, endDate time.Time) (*protocol.TopPerformers, error) {
	// 获取顶级实体
	entityMetrics, err := s.orderDAO.GetEntityPerformance(ctx, startDate, endDate, 10)
	if err != nil {
		return nil, err
	}

	var topEntities []*protocol.EntityPerformance
	for _, metric := range entityMetrics {
		profitMargin := float64(0)
		if metric.Revenue > 0 {
			profitMargin = float64(metric.Profit) / float64(metric.Revenue) * 100
		}

		entityName := i18n.I18N{
			Zh: fmt.Sprintf("实体_%d", metric.EntityId),
			En: fmt.Sprintf("Entity_%d", metric.EntityId),
		}

		topEntities = append(topEntities, &protocol.EntityPerformance{
			EntityId:   metric.EntityId,
			EntityName: entityName,
			TotalRevenue: money.Money{
				Amount:   float64(metric.Revenue),
				Currency: metric.Currency,
			},
			TotalProfit: money.Money{
				Amount:   float64(metric.Profit),
				Currency: "USD",
			},
			OrderCount:   metric.OrderCount,
			ProfitMargin: profitMargin,
		})
	}

	// 获取表现最佳日期
	dayMetrics, err := s.orderDAO.GetTopPerformingDays(ctx, startDate, endDate, 10)
	if err != nil {
		return nil, err
	}

	var topDays []*protocol.DayPerformance
	for _, metric := range dayMetrics {
		topDays = append(topDays, &protocol.DayPerformance{
			Date:       metric.Date,
			OrderCount: metric.TotalOrders,
			Revenue: money.Money{
				Amount:   float64(metric.TotalRevenue),
				Currency: metric.RevenueCurrency,
			},
			Profit: money.Money{
				Amount:   float64(metric.TotalProfit),
				Currency: "USD",
			},
		})
	}

	return &protocol.TopPerformers{
		TopEntities: topEntities,
		TopDays:     topDays,
		// TopCustomers 需要额外的用户数据，这里暂时留空
		TopCustomers: []*protocol.CustomerPerformance{},
	}, nil
}

// GetRealTimeMetrics 获取实时指标
func (s *OrderAnalyticsService) GetRealTimeMetrics(ctx context.Context, req *protocol.RealTimeMetricsReq) (*protocol.RealTimeMetricsResp, error) {
	metrics, err := s.orderDAO.GetRealTimeMetrics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get real-time metrics: %w", err)
	}

	totalOrders := metrics.TotalOrders
	if totalOrders == 0 {
		totalOrders = 1
	}

	completionRate := float64(metrics.CompletedOrders) / float64(totalOrders) * 100
	cancellationRate := float64(metrics.CancelledOrders) / float64(totalOrders) * 100

	return &protocol.RealTimeMetricsResp{
		TodayOrders: metrics.TotalOrders,
		TodayRevenue: money.Money{
			Amount:   float64(metrics.TotalRevenue),
			Currency: metrics.RevenueCurrency,
		},
		ActiveBookings:   metrics.CompletedOrders,
		PendingOrders:    metrics.PendingOrders,
		CompletionRate:   completionRate,
		CancellationRate: cancellationRate,
		UpdateTime:       time.Now(),
	}, nil
}

// calculateGrowthRate 计算增长率
func (s *OrderAnalyticsService) calculateGrowthRate(ctx context.Context, startDate, endDate time.Time, entityId *int64) (float64, error) {
	duration := endDate.Sub(startDate)
	prevStartDate := startDate.Add(-duration)
	prevEndDate := startDate

	currentMetrics, err := s.orderDAO.GetOrderOverview(ctx, startDate, endDate, entityId)
	if err != nil {
		return 0, err
	}

	prevMetrics, err := s.orderDAO.GetOrderOverview(ctx, prevStartDate, prevEndDate, entityId)
	if err != nil {
		return 0, err
	}

	if prevMetrics.TotalRevenue == 0 {
		return 0, nil
	}

	growthRate := float64(currentMetrics.TotalRevenue-prevMetrics.TotalRevenue) / float64(prevMetrics.TotalRevenue) * 100
	return growthRate, nil
}

// getStatusName 获取状态名称
func (s *OrderAnalyticsService) getStatusName(status int64) i18n.I18N {
	statusNames := map[int64]i18n.I18N{
		1: {Zh: "已创建", En: "Created"},
		2: {Zh: "已支付", En: "Paid"},
		3: {Zh: "等待确认", En: "Pending Confirmation"},
		4: {Zh: "已确认", En: "Confirmed"},
		5: {Zh: "已完成", En: "Completed"},
		6: {Zh: "已取消", En: "Cancelled"},
		7: {Zh: "等待取消", En: "Pending Cancellation"},
		8: {Zh: "等待退款", En: "Pending Refund"},
	}

	if name, exists := statusNames[status]; exists {
		return name
	}

	return i18n.I18N{
		Zh: fmt.Sprintf("状态_%d", status),
		En: fmt.Sprintf("Status_%d", status),
	}
}
