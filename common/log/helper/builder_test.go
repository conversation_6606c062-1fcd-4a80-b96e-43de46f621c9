package helper

import (
	"testing"

	"hotel/common/log"

	"github.com/smartystreets/goconvey/convey"
)

func TestLog(t *testing.T) {
	convey.Convey("test log", t, func() {
		lc := NewLogData("consume Approval msg")
		lc.Append("employee number", 123)
		lc.Append("requisition no", 123456)
		lc.AppendIfNonNil("nilkey", nil)
		lc.AppendIfNonNil("nonnilkey", 1)
		lc.AppendIfNonEmpty("nil_map", (map[string]string)(nil))
		lc.AppendIfNonEmpty("empty_map", map[string]string{})
		convey.So(lc.Build(), convey.ShouldEqual, "consume Approval msg: employee number(123) requisition no(123456) nonnilkey(1) empty_map()")
		convey.So(lc.ToMap(), convey.ShouldResemble, map[string]interface{}{
			"employee number": 123,
			"requisition no":  123456,
			"nonnilkey":       1,
			"empty_map":       map[string]string{},
		})
		log.Info(lc.Build())
	})
}
