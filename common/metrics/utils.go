// monitor/monitor.go
package metrics

import (
	"context"
	"fmt"
	"reflect"
	"runtime"
	"strings"
	"time"
)

// WrapMethod 将任意方法包装为监控方法
func WrapMethod(method interface{}) interface{} {
	methodVal := reflect.ValueOf(method)
	if methodVal.Kind() != reflect.Func {
		panic("WrapMethod requires a function input")
	}

	methodType := methodVal.Type()

	// 构建输入参数类型（context + 原方法参数）
	inputTypes := make([]reflect.Type, 0, methodType.NumIn()+1)
	inputTypes = append(inputTypes, reflect.TypeOf((*context.Context)(nil)).Elem())
	for i := 0; i < methodType.NumIn(); i++ {
		inputTypes = append(inputTypes, methodType.In(i))
	}

	// 构建输出参数类型
	outputTypes := make([]reflect.Type, methodType.NumOut())
	for i := range outputTypes {
		outputTypes[i] = methodType.Out(i)
	}

	// 创建包装函数类型
	wrapperType := reflect.FuncOf(inputTypes, outputTypes, false)

	// 构建包装函数
	wrapper := reflect.MakeFunc(wrapperType, func(args []reflect.Value) []reflect.Value {
		// 参数分解：context + 原方法参数
		ctx := args[0].Interface().(context.Context)
		methodArgs := args[1:]

		// 执行原方法并计时
		start := time.Now()
		results := methodVal.Call(methodArgs)
		duration := time.Since(start)

		// 上报监控数据
		ReportFromContext(ctx, methodVal, duration)

		return results
	})

	return wrapper.Interface()
}

// ReportFromContext 上报监控信息
func ReportFromContext(ctx context.Context, method reflect.Value, d time.Duration) {
	class, methodName := parseMethodName(method)
	tags := GetTagsFromContext(ctx)

	// 示例上报逻辑
	fmt.Printf("监控上报 - Class: %s, Method: %s, Duration: %v, Tags: %v\n",
		class, methodName, d, tags)
}

// parseMethodName 解析方法名称
func parseMethodName(method reflect.Value) (string, string) {
	funcPtr := method.Pointer()
	if funcPtr == 0 {
		return "unknown", "unknown"
	}

	fn := runtime.FuncForPC(funcPtr)
	if fn == nil {
		return "unknown", "unknown"
	}

	fullName := fn.Name()
	parts := strings.Split(fullName, ".")
	if len(parts) < 2 {
		return "global", fullName
	}

	classParts := strings.Split(parts[len(parts)-2], "/")
	className := classParts[len(classParts)-1]
	methodName := parts[len(parts)-1]

	// 移除方法指针后缀
	methodName = strings.TrimSuffix(methodName, "-fm")
	return className, methodName
}

// GetTagsFromContext 从上下文中获取标签
func GetTagsFromContext(ctx context.Context) map[string]string {
	if tags, ok := ctx.Value("metrics_tags").(map[string]string); ok {
		return tags
	}
	return make(map[string]string)
}
