package giata

import (
	"context"
	"encoding/xml"
	"hotel/mapping/supplier/giata/model"
	rmDomain "hotel/mapping/supplier/olivier/domain"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"
)

type GiataRoomMappingClient struct {
	*middleware.SupplierUtilWrapper[model.Properties]
}

func NewGiataRoomMappingClient() *GiataRoomMappingClient {
	configPath := "supplier/giata/config.yaml"
	return &GiataRoomMappingClient{
		SupplierUtilWrapper: middleware.SupplierUtilWrapperFromConfigFilePath[model.Properties](domain.Supplier_Giata, configPath),
	}
}

func (s *GiataRoomMappingClient) Supplier() domain.Supplier {
	return domain.Supplier_Giata
}

// RoomMatching provides GIATA-based room matching using multicodes
func (s *GiataRoomMappingClient) RoomMatching(ctx context.Context, req *rmDomain.RoomMatchingReq) (*rmDomain.RoomMatchingResp, error) {
	// GIATA room mapping implementation
	// For now, return a basic response - full implementation would integrate with GIATA Multicodes API
	
	resp := &rmDomain.RoomMatchingResp{
		Matching: make([]rmDomain.RoomMatch, 0),
	}
	
	// Group rooms by similar names for basic matching
	roomGroups := s.groupRoomsByName(req.List)
	
	for roomName, rooms := range roomGroups {
		if len(rooms) > 1 { // Only create matches for rooms with multiple suppliers
			match := rmDomain.RoomMatch{
				RoomName:  roomName,
				Suppliers: make([]rmDomain.RoomMatchSupplierItem, 0),
			}
			
			for _, room := range rooms {
				supplierItem := rmDomain.RoomMatchSupplierItem{
					Supplier: room.Supplier,
					HotelID:  room.HotelID,
					RoomKey: rmDomain.RoomKey{
						RoomID:     room.RoomCode1, // Use RoomCode1 as RoomID
						RoomCode:   room.RoomCode2, // Use RoomCode2 as RoomCode
						Confidence: 0.8,            // Basic confidence score
					},
				}
				match.Suppliers = append(match.Suppliers, supplierItem)
			}
			
			resp.Matching = append(resp.Matching, match)
		}
	}
	
	return resp, nil
}

// groupRoomsByName groups rooms by similar names for basic matching
func (s *GiataRoomMappingClient) groupRoomsByName(rooms []rmDomain.RoomInfo) map[string][]rmDomain.RoomInfo {
	groups := make(map[string][]rmDomain.RoomInfo)
	
	for _, room := range rooms {
		// Simple grouping by room name
		// In a full implementation, this would use GIATA's standardized room types
		roomName := room.Name
		if roomName == "" {
			roomName = "Unknown"
		}
		
		groups[roomName] = append(groups[roomName], room)
	}
	
	return groups
}

// For future GIATA Multicodes integration
func (s *GiataRoomMappingClient) getGiataRoomTypes(ctx context.Context, hotelID string) (*model.GiataRoomTypesResponse, error) {
	// Future implementation for GIATA Multicodes API
	giataReq := map[string]string{
		"sc":   "roomtype",
		"vc":   "GIATA",
		"oc":   hotelID,
		"show": "fact,rt",
	}
	
	var respXml string
	if err := s.Execute(ctx, domain.APIName_HotelStaticDetail, giataReq, &respXml); err != nil {
		return nil, err
	}
	
	giataResp := new(model.GiataRoomTypesResponse)
	if err := xml.Unmarshal([]byte(respXml), giataResp); err != nil {
		return nil, err
	}
	
	return giataResp, nil
}