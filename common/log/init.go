package log

import (
	"context"
	"os"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"

	"hotel/common/envhelper"
)

var h *log.Helper

func init() {
	log.SetLogger(log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.Caller(5),
		"service.name", ServiceName(),
		"env", ENV(),
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
	))
	h = log.NewHelper(log.GetLogger())
}

// ServiceName .
func ServiceName() log.Valuer {
	return func(context.Context) any {
		return envhelper.GetServiceName()
	}
}

// ENV .
func ENV() log.Valuer {
	return func(context.Context) any {
		return envhelper.GetENV()
	}
}
