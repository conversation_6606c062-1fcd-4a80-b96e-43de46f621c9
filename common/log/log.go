package log

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/trace"
)

// GenerateLogID 生成一个基于时间戳和UUID的唯一logid
func GenerateLogID() string {
	return fmt.Sprintf("%d-%s", time.Now().UnixNano(), uuid.New().String())
}

// GetOrNewLogidFromContext 从 context 中获取 logid
func GetOrNewLogidFromContext(ctx context.Context) string {
	if v := trace.TraceIDFromContext(ctx); v != "" {
		return v
	}
	return GenerateLogID()
}
func GetLogidFromContext(ctx context.Context) string {
	if v := trace.TraceIDFromContext(ctx); v != "" {
		return v
	}
	return ""
}
