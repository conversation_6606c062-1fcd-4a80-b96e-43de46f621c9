# Link Technology

```mermaid
sequenceDiagram
    title Link-Technology
    actor B as Buyer (Customer)
    participant BI as Buyer.InNode
    participant SI as Seller.InNode
    actor S as Seller (Supply Endpoint)
    participant SO as Seller.OutNode
    participant BO as Buyer.OutNode
    Note over BI, SI: 请求处理阶段
    B ->> BI: 发送请求
    activate BI
    BI ->> BI: buyer 的credential鉴权（由平台发放）
    BI ->> BI: 应用买家前置规则（过滤条件），<br>拿到 seller 的 credential（由 seller 直接给 buyer 发放，或者 seller 经 platform 间接给 buyer 授予）
    BI ->> SI: 携带 seller 的 credential
    activate SI
    SI ->> SI: 应用卖家前置规则<br>（价格策略/过滤条件）
    SI ->> S: 向供应商系统发送请求（注：平台会为授权客户开启一个供应端点，将其视为供应商系统处理）
    activate SO
    Note over SO, BO: 响应处理阶段
    S ->> SO: 返回原始供应商响应数据
    SO -->> SO: 应用卖家响应规则<br>（价格策略/过滤条件）
    SO -->> BO: 统一供应商响应
    BO ->> BO: 应用买家后置规则<br>（价格策略/过滤条件）
    deactivate SO
    BO -->> B: 返回平台处理结果
    deactivate SI
```

```mermaid
sequenceDiagram
    title Link-Technology
    actor B as Buyer (Customer)
    participant BI as Buyer.InNode
    participant SI as Seller.InNode
    actor S as Seller (Supply)
    participant SO as Seller.OutNode
    participant BO as Buyer.OutNode
    Note over BI, SI: Request Processing Phase
    B ->> BI: Send Request
    activate BI
    BI ->> BI: Authenticate buyer's credential (issued by the platform）
    BI ->> BI: Apply buyer pre-rules (filter conditions),<br> obtain seller's credential (issued directly to the buyer by the seller,<br> or indirectly granted to the buyer by the seller via the platform)
    BI ->> SI: Carry seller's credential
    activate SI
    SI ->> SI: Apply seller pre-rules<br>（pricing strategy/filter conditions)
    SI ->> S: Send request to the supply endpoint<br> (Note: we'll create a supply endpoint <br>for each authorized client<br> to empower market place)
    activate SO
    Note over SO, BO: Response Processing Phase
    S ->> SO: Return raw supplier response data
    SO -->> SO: Apply seller response rules<br> (pricing strategy/filter conditions)
    SO -->> BO: Universal Supplier response
    BO ->> BO: Apply buyer post-rules<br> (pricing strategy/filter conditions)
    deactivate SO
    BO -->> B: Return platform processing result
    deactivate SI
```


# FAQs
- 如何将没有接口的客户变身成为卖家？
  - 首先，客户在平台首先可以成为一个买家；
  - 然后平台会为授权客户开启一个供应端点，将其视为供应商系统处理。
  - 当然另一种方式是 extranet 模式，由平台完全托管。
- 如何将没有供应商credential的客户变身成为买家？
  - 在平台壮大后，会直接取得供应商的credential，然后通过中间商转售的方式提供给买家。