# 订单重预订模块

## 概述

订单重预订模块是酒店预订平台的一个智能优化子系统，旨在在免费取消时间窗口内，自动监控并发现更具性价比的酒店房型选择，并自动执行重预订操作以为客户节省成本。

## 功能特性

### 1. 自动扫描与监控
- **定时扫描**: 默认每30分钟扫描一次符合条件的订单
- **智能过滤**: 只处理已确认、未入住且在免费取消窗口内的订单
- **并发处理**: 支持多订单并发重预订，提高处理效率

### 2. 智能决策引擎
- **多供应商比价**: 同时查询所有可用供应商的实时价格
- **收益计算**: 精确计算重预订可能带来的成本节省
- **风险控制**: 设置最小节省阈值，避免频繁重预订

### 3. 完整的重预订流程
- **先订后取**: 先预订新房型，确认成功后再取消原订单
- **失败回滚**: 如果重预订失败，保持原订单不变
- **历史记录**: 完整记录每次重预订的详细信息

### 4. 手动操作支持
- **手动触发**: 支持管理员手动触发特定订单的重预订
- **选项预览**: 可以预览重预订选项而不执行操作
- **历史查询**: 查看订单的重预订历史记录

## 核心组件

### 1. RebookingService
主要服务类，提供重预订功能的所有对外接口。

**主要方法:**
- `ManualRebooking`: 手动触发重预订
- `GetRebookingOptions`: 获取重预订选项
- `GetRebookingHistory`: 查看重预订历史
- `SetRebookingConfig`: 配置重预订参数

### 2. RebookingScanner
后台扫描器，负责定时扫描和自动重预订。

**主要功能:**
- 定时扫描符合条件的订单
- 并发处理多个重预订任务
- 错误处理和重试机制

### 3. RebookingOption
重预订选项数据结构，包含价格、供应商等信息。

## API 接口

### 1. 手动重预订
```http
POST /rebooking/manual
Content-Type: application/json

{
  "orderId": "12345"
}
```

**响应:**
```json
{
  "success": true,
  "message": "Rebooking completed successfully",
  "originalPrice": 50000,
  "newPrice": 48000,
  "savings": 2000,
  "newSupplier": "dida",
  "newRatePkgId": "rate_456"
}
```

### 2. 获取重预订选项
```http
POST /rebooking/options
Content-Type: application/json

{
  "orderId": "12345"
}
```

**响应:**
```json
{
  "orderId": "12345",
  "eligible": true,
  "message": "Found 3 rebooking options",
  "options": [
    {
      "supplier": "dida",
      "ratePkgId": "rate_456",
      "newPrice": 48000,
      "originalPrice": 50000,
      "priceDifference": -2000,
      "savings": 2000,
      "roomType": "Superior Room"
    }
  ]
}
```

### 3. 获取重预订历史
```http
POST /rebooking/history
Content-Type: application/json

{
  "orderId": "12345"
}
```

**响应:**
```json
{
  "orderId": "12345",
  "history": [
    {
      "executedAt": "2025-01-15T10:30:00Z",
      "originalPrice": 50000,
      "newPrice": 48000,
      "savings": 2000,
      "supplier": "dida",
      "originalOrderId": "orig_123",
      "newOrderId": "new_456"
    }
  ]
}
```

### 4. 设置重预订配置
```http
POST /rebooking/config
Content-Type: application/json

{
  "minSavings": 1000,
  "scanInterval": 1800,
  "enabledSuppliers": ["dida", "tbo"],
  "timeWindowHours": 48,
  "rebookingEnabled": true
}
```

## 配置参数

### 重预订条件
- **订单状态**: 必须是已确认状态
- **时间窗口**: 默认入住前48小时内可重预订
- **最小节省**: 默认节省10元以上才执行重预订
- **扫描间隔**: 默认30分钟扫描一次

### 可配置参数
- `minSavings`: 最小节省金额阈值（分）
- `scanInterval`: 扫描间隔（秒）
- `enabledSuppliers`: 启用的供应商列表
- `timeWindowHours`: 重预订时间窗口（小时）
- `rebookingEnabled`: 是否启用重预订功能

## 数据存储

### 订单表扩展
重预订信息存储在订单表的 `biz_info` 字段中，JSON格式：

```json
{
  "rebooking": {
    "executed_at": "2025-01-15T10:30:00Z",
    "original_supplier_order": "orig_123",
    "new_supplier_order": "new_456",
    "original_price": 50000,
    "new_price": 48000,
    "savings": 2000,
    "supplier": "dida"
  },
  "rebooking_config": {
    "disabled": false
  }
}
```

## 使用示例

### 1. 启动重预订服务
```go
// 在 TradeService 初始化时自动创建
tradeService := NewTradeService()

// 启动后台扫描器
ctx := context.Background()
err := tradeService.StartRebookingScanner(ctx)
if err != nil {
    log.Error("Failed to start rebooking scanner: %v", err)
}
```

### 2. 手动触发重预订
```go
req := &protocol.ManualRebookingReq{
    OrderId: "12345",
}

resp, err := tradeService.ManualRebooking(ctx, req)
if err != nil {
    log.Error("Manual rebooking failed: %v", err)
    return
}

if resp.Success {
    log.Info("Rebooking successful, saved %d cents", resp.Savings)
}
```

### 3. 查看重预订选项
```go
req := &protocol.GetRebookingOptionsReq{
    OrderId: "12345",
}

resp, err := tradeService.GetRebookingOptions(ctx, req)
if err != nil {
    log.Error("Get rebooking options failed: %v", err)
    return
}

log.Info("Found %d rebooking options", len(resp.Options))
for _, option := range resp.Options {
    log.Info("Option: %s, savings: %d", option.Supplier, option.Savings)
}
```

## 监控与告警

### 关键指标
- 重预订成功率
- 平均节省金额
- 处理延迟
- 错误率

### 日志记录
所有重预订操作都会记录详细日志，包括：
- 扫描开始/结束时间
- 找到的符合条件订单数量
- 每个订单的重预订结果
- 错误和异常信息

### 示例日志
```
[INFO] Starting rebooking scanner with interval: 30m0s
[INFO] Found 5 eligible orders for rebooking
[INFO] Processing rebooking for order 12345
[INFO] Found better option for order 12345: supplier=dida, savings=2000
[INFO] Successfully booked new room for order 12345: supplier_order_id=new_456
[INFO] Executing rebooking for order 12345 with supplier dida
```

## 性能优化

### 1. 并发处理
- 使用协程并发处理多个订单的重预订
- 控制并发数量避免过载供应商API

### 2. 缓存策略
- 缓存供应商响应减少重复查询
- 缓存配置信息减少数据库访问

### 3. 批量操作
- 批量查询符合条件的订单
- 批量更新订单状态

## 错误处理

### 1. 重试机制
- 供应商API调用失败时自动重试
- 使用指数退避策略避免频繁重试

### 2. 失败恢复
- 重预订失败时保持原订单不变
- 记录失败原因便于排查

### 3. 异常监控
- 监控异常率和错误类型
- 及时发现和解决问题

## 测试

### 单元测试
```bash
cd trade/service
go test -v -run TestRebooking
```

### 集成测试
```bash
go test -v -run TestRebooking.*Integration
```

### 性能测试
```bash
go test -v -bench=BenchmarkRebooking
```

## 安全考虑

### 1. 权限控制
- 只有授权用户可以手动触发重预订
- 配置修改需要管理员权限

### 2. 数据保护
- 敏感信息加密存储
- API调用使用安全认证

### 3. 审计日志
- 记录所有重预订操作
- 包含操作用户和时间戳

## 未来扩展

### 1. 规则引擎集成
- 支持更复杂的重预订决策规则
- 考虑客户偏好和历史行为

### 2. 机器学习优化
- 预测最佳重预订时机
- 学习客户接受度提高成功率

### 3. 多维度优化
- 不仅考虑价格，还考虑房型升级
- 综合评估客户满意度和成本

## 故障排除

### 常见问题

1. **重预订不生效**
   - 检查订单状态是否符合条件
   - 验证时间窗口设置
   - 确认供应商API可用性

2. **扫描器未启动**
   - 检查服务初始化日志
   - 验证配置文件正确性
   - 确认消息队列连接正常

3. **节省金额计算错误**
   - 检查价格字段映射
   - 验证币种转换逻辑
   - 确认税费和手续费计算

### 调试命令
```bash
# 查看重预订日志
grep "rebooking" /var/log/hotel-be/trade.log

# 测试单个订单重预订
curl -X POST http://localhost:8080/rebooking/options \
  -H "Content-Type: application/json" \
  -d '{"orderId": "12345"}'
```

## 联系方式

如有问题或建议，请联系开发团队：
- 邮箱: <EMAIL>
- 文档: [内部wiki链接]
- 代码仓库: [git仓库链接]