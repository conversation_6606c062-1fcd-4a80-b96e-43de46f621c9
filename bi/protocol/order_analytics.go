package protocol

import (
	"hotel/common/i18n"
	"hotel/common/money"
	"time"
)

// OrderAnalyticsReq 订单分析请求
type OrderAnalyticsReq struct {
	StartDate    *time.Time `json:"startDate,omitempty"`    // 开始日期
	EndDate      *time.Time `json:"endDate,omitempty"`      // 结束日期
	Granularity  string     `json:"granularity,omitempty"`  // 粒度: day/week/month/year
	StatusFilter []int64    `json:"statusFilter,omitempty"` // 状态过滤
	EntityId     *int64     `json:"entityId,omitempty"`     // 实体ID过滤
}

// OrderAnalyticsResp 订单分析响应
type OrderAnalyticsResp struct {
	Overview         *OrderOverview      `json:"overview"`         // 总览数据
	TrendData        []*OrderTrendData   `json:"trendData"`        // 趋势数据
	StatusBreakdown  []*OrderStatusStat  `json:"statusBreakdown"`  // 状态分布
	RevenueAnalysis  *RevenueAnalysis    `json:"revenueAnalysis"`  // 收入分析
	TopPerformers    *TopPerformers      `json:"topPerformers"`    // 表现排行
	RegionalAnalysis []*RegionalStat     `json:"regionalAnalysis"` // 区域分析
}

// OrderOverview 订单总览
type OrderOverview struct {
	TotalOrders       int64       `json:"totalOrders"`       // 总订单数
	TotalRevenue      money.Money `json:"totalRevenue"`      // 总收入
	TotalProfit       money.Money `json:"totalProfit"`       // 总利润
	AverageOrderValue money.Money `json:"averageOrderValue"` // 平均订单价值
	BookingRate       float64     `json:"bookingRate"`       // 预订转化率
	CancellationRate  float64     `json:"cancellationRate"`  // 取消率
	CompletionRate    float64     `json:"completionRate"`    // 完成率
	GrowthRate        float64     `json:"growthRate"`        // 增长率(相比上期)
}

// OrderTrendData 订单趋势数据
type OrderTrendData struct {
	Date         time.Time   `json:"date"`         // 日期
	OrderCount   int64       `json:"orderCount"`   // 订单数量
	Revenue      money.Money `json:"revenue"`      // 收入
	Profit       money.Money `json:"profit"`       // 利润
	BookingCount int64       `json:"bookingCount"` // 预订数量
	CancelCount  int64       `json:"cancelCount"`  // 取消数量
}

// OrderStatusStat 订单状态统计
type OrderStatusStat struct {
	Status      int64          `json:"status"`      // 状态码
	StatusName  i18n.I18N      `json:"statusName"`  // 状态名称
	Count       int64          `json:"count"`       // 数量
	Percentage  float64        `json:"percentage"`  // 占比
	Revenue     money.Money    `json:"revenue"`     // 该状态订单总收入
	AvgDuration *time.Duration `json:"avgDuration"` // 平均持续时间
}

// RevenueAnalysis 收入分析
type RevenueAnalysis struct {
	TotalRevenue      money.Money          `json:"totalRevenue"`      // 总收入
	TotalCost         money.Money          `json:"totalCost"`         // 总成本
	TotalProfit       money.Money          `json:"totalProfit"`       // 总利润
	ProfitMargin      float64              `json:"profitMargin"`      // 利润率
	RevenueByCurrency []*CurrencyRevenue   `json:"revenueByCurrency"` // 按币种收入
	RevenueByEntity   []*EntityRevenue     `json:"revenueByEntity"`   // 按实体收入
	MonthlyComparison []*MonthlyComparison `json:"monthlyComparison"` // 月度对比
}

// CurrencyRevenue 币种收入
type CurrencyRevenue struct {
	Currency   string      `json:"currency"`   // 币种
	Revenue    money.Money `json:"revenue"`    // 收入
	OrderCount int64       `json:"orderCount"` // 订单数
	Percentage float64     `json:"percentage"` // 占比
}

// EntityRevenue 实体收入
type EntityRevenue struct {
	EntityId   int64       `json:"entityId"`   // 实体ID
	EntityName i18n.I18N   `json:"entityName"` // 实体名称
	Revenue    money.Money `json:"revenue"`    // 收入
	Profit     money.Money `json:"profit"`     // 利润
	OrderCount int64       `json:"orderCount"` // 订单数
	GrowthRate float64     `json:"growthRate"` // 增长率
}

// MonthlyComparison 月度对比
type MonthlyComparison struct {
	Month      time.Time   `json:"month"`      // 月份
	Revenue    money.Money `json:"revenue"`    // 收入
	Profit     money.Money `json:"profit"`     // 利润
	OrderCount int64       `json:"orderCount"` // 订单数
	GrowthRate float64     `json:"growthRate"` // 增长率
}

// TopPerformers 表现排行
type TopPerformers struct {
	TopCustomers []*CustomerPerformance `json:"topCustomers"` // 顶级客户
	TopEntities  []*EntityPerformance   `json:"topEntities"`  // 顶级实体
	TopDays      []*DayPerformance      `json:"topDays"`      // 表现最佳日期
}

// CustomerPerformance 客户表现
type CustomerPerformance struct {
	CustomerId    int64       `json:"customerId"`    // 客户ID
	CustomerName  i18n.I18N   `json:"customerName"`  // 客户名称
	TotalRevenue  money.Money `json:"totalRevenue"`  // 总收入
	OrderCount    int64       `json:"orderCount"`    // 订单数
	AvgOrderValue money.Money `json:"avgOrderValue"` // 平均订单价值
	LastOrderDate time.Time   `json:"lastOrderDate"` // 最后订单日期
}

// EntityPerformance 实体表现
type EntityPerformance struct {
	EntityId     int64       `json:"entityId"`     // 实体ID
	EntityName   i18n.I18N   `json:"entityName"`   // 实体名称
	TotalRevenue money.Money `json:"totalRevenue"` // 总收入
	TotalProfit  money.Money `json:"totalProfit"`  // 总利润
	OrderCount   int64       `json:"orderCount"`   // 订单数
	ProfitMargin float64     `json:"profitMargin"` // 利润率
}

// DayPerformance 日期表现
type DayPerformance struct {
	Date       time.Time   `json:"date"`       // 日期
	Revenue    money.Money `json:"revenue"`    // 收入
	OrderCount int64       `json:"orderCount"` // 订单数
	Profit     money.Money `json:"profit"`     // 利润
}

// RegionalStat 区域统计
type RegionalStat struct {
	Region     string      `json:"region"`     // 区域
	OrderCount int64       `json:"orderCount"` // 订单数
	Revenue    money.Money `json:"revenue"`    // 收入
	Profit     money.Money `json:"profit"`     // 利润
	GrowthRate float64     `json:"growthRate"` // 增长率
}

// OrderMetricsReq 订单指标请求
type OrderMetricsReq struct {
	MetricType  string                 `json:"metricType"` // 指标类型: overview/trend/status/revenue
	StartDate   *time.Time             `json:"startDate,omitempty"`
	EndDate     *time.Time             `json:"endDate,omitempty"`
	Granularity string                 `json:"granularity,omitempty"` // day/week/month
	Filters     map[string]interface{} `json:"filters,omitempty"`     // 其他过滤条件
}

// OrderMetricsResp 订单指标响应
type OrderMetricsResp struct {
	MetricType string      `json:"metricType"` // 指标类型
	Data       interface{} `json:"data"`       // 具体数据，根据MetricType动态决定结构
	UpdateTime time.Time   `json:"updateTime"` // 数据更新时间
}

// RealTimeMetricsReq 实时指标请求
type RealTimeMetricsReq struct {
	Metrics []string `json:"metrics"` // 需要的指标列表
}

// RealTimeMetricsResp 实时指标响应
type RealTimeMetricsResp struct {
	TodayOrders      int64       `json:"todayOrders"`      // 今日订单
	TodayRevenue     money.Money `json:"todayRevenue"`     // 今日收入
	ActiveBookings   int64       `json:"activeBookings"`   // 活跃预订
	PendingOrders    int64       `json:"pendingOrders"`    // 待处理订单
	CompletionRate   float64     `json:"completionRate"`   // 完成率
	CancellationRate float64     `json:"cancellationRate"` // 取消率
	UpdateTime       time.Time   `json:"updateTime"`       // 更新时间
}

// ExportReq 导出请求
type ExportReq struct {
	ExportType string                 `json:"exportType"` // 导出类型: excel/csv/pdf
	DataType   string                 `json:"dataType"`   // 数据类型: orders/analytics/revenue
	StartDate  *time.Time             `json:"startDate,omitempty"`
	EndDate    *time.Time             `json:"endDate,omitempty"`
	Filters    map[string]interface{} `json:"filters,omitempty"`
	Fields     []string               `json:"fields,omitempty"` // 需要导出的字段
}

// ExportResp 导出响应
type ExportResp struct {
	FileUrl    string    `json:"fileUrl"`    // 文件下载链接
	FileName   string    `json:"fileName"`   // 文件名
	FileSize   int64     `json:"fileSize"`   // 文件大小
	ExpiryTime time.Time `json:"expiryTime"` // 过期时间
}