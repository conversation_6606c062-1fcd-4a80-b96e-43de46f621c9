package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// OrderAnalyticsDAO 订单分析数据访问对象
type OrderAnalyticsDAO struct {
	conn  sqlx.SqlConn
	table string
}

// NewOrderAnalyticsDAO 创建订单分析DAO
func NewOrderAnalyticsDAO(conn sqlx.SqlConn) *OrderAnalyticsDAO {
	return &OrderAnalyticsDAO{
		conn:  conn,
		table: "`order`",
	}
}

// OrderMetrics 订单指标结构
type OrderMetrics struct {
	Date            time.Time `db:"date"`
	TotalOrders     int64     `db:"total_orders"`
	TotalRevenue    int64     `db:"total_revenue"`
	TotalCost       int64     `db:"total_cost"`
	TotalProfit     int64     `db:"total_profit"`
	CompletedOrders int64     `db:"completed_orders"`
	CancelledOrders int64     `db:"cancelled_orders"`
	PendingOrders   int64     `db:"pending_orders"`
	RevenueCurrency string    `db:"revenue_currency"`
}

// StatusMetrics 状态指标
type StatusMetrics struct {
	Status   int64  `db:"status"`
	Count    int64  `db:"count"`
	Revenue  int64  `db:"revenue"`
	Currency string `db:"currency"`
}

// EntityMetrics 实体指标
type EntityMetrics struct {
	EntityId   int64  `db:"entity_id"`
	EntityName string `db:"entity_name"`
	OrderCount int64  `db:"order_count"`
	Revenue    int64  `db:"revenue"`
	Profit     int64  `db:"profit"`
	Currency   string `db:"currency"`
}

// GetOrderMetricsByDateRange 获取指定日期范围的订单指标
func (dao *OrderAnalyticsDAO) GetOrderMetricsByDateRange(ctx context.Context, startDate, endDate time.Time, granularity string) ([]*OrderMetrics, error) {
	var dateFormat string
	switch granularity {
	case "day":
		dateFormat = "%Y-%m-%d"
	case "week":
		dateFormat = "%Y-%u"
	case "month":
		dateFormat = "%Y-%m"
	case "year":
		dateFormat = "%Y"
	default:
		dateFormat = "%Y-%m-%d"
	}

	query := fmt.Sprintf(`
		SELECT 
			DATE_FORMAT(create_time, '%s') as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			COALESCE(SUM(seller_amount), 0) as total_cost,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			buyer_currency as revenue_currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ?
		GROUP BY DATE_FORMAT(create_time, '%s'), buyer_currency
		ORDER BY date ASC`, dateFormat, dao.table, dateFormat)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate)
	return metrics, err
}

// GetOrderOverview 获取订单总览
func (dao *OrderAnalyticsDAO) GetOrderOverview(ctx context.Context, startDate, endDate time.Time, entityId *int64) (*OrderMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ?"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			NOW() as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			COALESCE(SUM(seller_amount), 0) as total_cost,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s %s`, dao.table, whereClause)

	var overview OrderMetrics
	err := dao.conn.QueryRowCtx(ctx, &overview, query, args...)
	return &overview, err
}

// GetStatusBreakdown 获取状态分布
func (dao *OrderAnalyticsDAO) GetStatusBreakdown(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*StatusMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ?"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			status,
			COUNT(*) as count,
			COALESCE(SUM(buyer_amount), 0) as revenue,
			buyer_currency as currency
		FROM %s %s
		GROUP BY status, buyer_currency
		ORDER BY status ASC`, dao.table, whereClause)

	var metrics []*StatusMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, args...)
	return metrics, err
}

// GetEntityPerformance 获取实体表现
func (dao *OrderAnalyticsDAO) GetEntityPerformance(ctx context.Context, startDate, endDate time.Time, limit int64) ([]*EntityMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			customer_entity_id as entity_id,
			CONCAT('Entity_', customer_entity_id) as entity_name,
			COUNT(*) as order_count,
			COALESCE(SUM(buyer_amount), 0) as revenue,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as profit,
			buyer_currency as currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ?
		GROUP BY customer_entity_id, buyer_currency
		ORDER BY revenue DESC
		LIMIT ?`, dao.table)

	var metrics []*EntityMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate, limit)
	return metrics, err
}

// GetRevenueByEntity 按实体获取收入数据
func (dao *OrderAnalyticsDAO) GetRevenueByEntity(ctx context.Context, startDate, endDate time.Time) ([]*EntityMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			customer_entity_id as entity_id,
			CONCAT('Entity_', customer_entity_id) as entity_name,
			COUNT(*) as order_count,
			COALESCE(SUM(buyer_amount), 0) as revenue,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as profit,
			buyer_currency as currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ?
		GROUP BY customer_entity_id, buyer_currency
		ORDER BY revenue DESC`, dao.table)

	var metrics []*EntityMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate)
	return metrics, err
}

// GetDailyTrend 获取每日趋势数据
func (dao *OrderAnalyticsDAO) GetDailyTrend(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*OrderMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ?"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			DATE(create_time) as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			COALESCE(SUM(seller_amount), 0) as total_cost,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s %s
		GROUP BY DATE(create_time)
		ORDER BY date ASC`, dao.table, whereClause)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, args...)
	return metrics, err
}

// GetRevenueByCurrency 按币种获取收入
func (dao *OrderAnalyticsDAO) GetRevenueByCurrency(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*StatusMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ?"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			0 as status,
			COUNT(*) as count,
			COALESCE(SUM(buyer_amount), 0) as revenue,
			buyer_currency as currency
		FROM %s %s
		GROUP BY buyer_currency
		ORDER BY revenue DESC`, dao.table, whereClause)

	var metrics []*StatusMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, args...)
	return metrics, err
}

// GetTopPerformingDays 获取表现最佳的日期
func (dao *OrderAnalyticsDAO) GetTopPerformingDays(ctx context.Context, startDate, endDate time.Time, limit int64) ([]*OrderMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			DATE(create_time) as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			COALESCE(SUM(seller_amount), 0) as total_cost,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ?
		GROUP BY DATE(create_time)
		ORDER BY total_revenue DESC
		LIMIT ?`, dao.table)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate, limit)
	return metrics, err
}

// GetRealTimeMetrics 获取实时指标
func (dao *OrderAnalyticsDAO) GetRealTimeMetrics(ctx context.Context) (*OrderMetrics, error) {
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)

	query := fmt.Sprintf(`
		SELECT 
			NOW() as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			COALESCE(SUM(seller_amount), 0) as total_cost,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s 
		WHERE create_time >= ? AND create_time < ?`, dao.table)

	var metrics OrderMetrics
	err := dao.conn.QueryRowCtx(ctx, &metrics, query, today, tomorrow)
	return &metrics, err
}

// GetMonthlyComparison 获取月度对比数据
func (dao *OrderAnalyticsDAO) GetMonthlyComparison(ctx context.Context, months int) ([]*OrderMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			DATE_FORMAT(create_time, '%%Y-%%m-01') as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			COALESCE(SUM(seller_amount), 0) as total_cost,
			COALESCE(SUM(tenant_revenue_amount_usd + customer_revenue_amount_usd), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s 
		WHERE create_time >= DATE_SUB(NOW(), INTERVAL ? MONTH)
		GROUP BY DATE_FORMAT(create_time, '%%Y-%%m')
		ORDER BY date ASC`, dao.table)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, months)
	return metrics, err
}
