package log

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

type Level = log.Level

const (
	// LevelDebug is logger debug level.
	LevelDebug = log.LevelDebug
	// LevelInfo is logger info level.
	LevelInfo = log.LevelInfo
	// LevelWarn is logger warn level.
	LevelWarn = log.LevelWarn
	// LevelError is logger error level.
	LevelError = log.LevelError
	// LevelFatal is logger fatal level
	LevelFatal = log.LevelFatal
)

// Log Print log by level and keyvals.
func Log(level log.Level, keyvals ...any) {
	h.Log(level, keyvals...)
}

// Logc Print log by level and keyvals.
func Logc(ctx context.Context, level log.Level, keyvals ...any) {
	h.WithContext(ctx).Log(level, keyvals...)
}

// Debug logs a message at debug level.
func Debug(format string, a ...any) {
	h.Debug(a...)
}

// Debugc logs a message at debug level.
func Debugc(ctx context.Context, format string, a ...any) {
	h.WithContext(ctx).Debug(a...)
}

// Debugw logs a message at debug level.
func Debugw(keyvals ...any) {
	h.Debugw(keyvals...)
}

// Debugwc logs a message at debug level.
func Debugwc(ctx context.Context, keyvals ...any) {
	h.WithContext(ctx).Debugw(keyvals...)
}

// Info logs a message at info level.
func Info(format string, a ...any) {
	h.Infof(format, a...)
}

// Infoc logs a message at info level.
func Infoc(ctx context.Context, format string, a ...any) {
	h.WithContext(ctx).Infof(format, a...)
}

// Infow logs a message at info level.
func Infow(keyvals ...any) {
	h.Infow(keyvals...)
}

// Infowc logs a message at info level.
func Infowc(ctx context.Context, keyvals ...any) {
	h.WithContext(ctx).Infow(keyvals...)
}

// Warn logs a message at warn level.
func Warn(format string, a ...any) {
	h.Warnf(format, a...)
}
func Warnc(ctx context.Context, format string, a ...any) {
	h.WithContext(ctx).Warnf(format, a...)
}

// Warnw logs a message at warnf level.
func Warnw(keyvals ...any) {
	h.Warnw(keyvals...)
}
func Warnwc(ctx context.Context, keyvals ...any) {
	h.WithContext(ctx).Warnw(keyvals...)
}

// Error logs a message at error level.
func Error(format string, a ...any) {
	h.Errorf(format, a...)
}
func Errorc(ctx context.Context, format string, a ...any) {
	h.WithContext(ctx).Errorf(format, a...)
}

// Errorw logs a message at error level.
func Errorw(keyvals ...any) {
	h.Errorw(keyvals...)
}

// Errorwc logs a message at error level.
func Errorwc(ctx context.Context, keyvals ...any) {
	h.WithContext(ctx).Errorw(keyvals...)
}

// Fatal logs a message at fatal level.
func Fatal(format string, a ...any) {
	h.Fatalf(format, a...)
}

// Fatalc logs a message at fatal level.
func Fatalc(ctx context.Context, format string, a ...any) {
	h.WithContext(ctx).Fatalf(format, a...)
}

// Fatalw logs a message at fatal level.
func Fatalw(keyvals ...any) {
	h.Fatalw(keyvals...)
}

// Fatalwc logs a message at fatal level.
func Fatalwc(ctx context.Context, keyvals ...any) {
	h.WithContext(ctx).Fatalw(keyvals...)
}
