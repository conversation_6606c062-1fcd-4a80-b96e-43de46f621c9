package service

import (
	"context"
	"testing"
	"time"

	"hotel/bi/protocol"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockOrderAnalyticsService 模拟分析服务
type MockOrderAnalyticsService struct {
	mock.Mock
}

func (m *MockOrderAnalyticsService) GetOrderAnalytics(ctx context.Context, req *protocol.OrderAnalyticsReq) (*protocol.OrderAnalyticsResp, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*protocol.OrderAnalyticsResp), args.Error(1)
}

func (m *MockOrderAnalyticsService) GetRealTimeMetrics(ctx context.Context, req *protocol.RealTimeMetricsReq) (*protocol.RealTimeMetricsResp, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*protocol.RealTimeMetricsResp), args.Error(1)
}

func TestOrderBIService_GetOrderAnalytics(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 准备测试数据
	startDate := time.Now().AddDate(0, -1, 0)
	endDate := time.Now()

	mockResp := &protocol.OrderAnalyticsResp{
		Overview: &protocol.OrderOverview{
			TotalOrders:       100,
			TotalRevenue:      protocol.Money{Amount: 1000000, Currency: "USD"},
			TotalProfit:       protocol.Money{Amount: 200000, Currency: "USD"},
			CompletionRate:    85.5,
			CancellationRate:  8.2,
			GrowthRate:        12.5,
		},
		TrendData: []*protocol.OrderTrendData{
			{
				Date:         startDate,
				OrderCount:   10,
				Revenue:      protocol.Money{Amount: 100000, Currency: "USD"},
				Profit:       protocol.Money{Amount: 20000, Currency: "USD"},
				BookingCount: 8,
				CancelCount:  1,
			},
		},
		StatusBreakdown: []*protocol.OrderStatusStat{
			{
				Status:     1,
				StatusName: protocol.I18N{"zh-CN": "已创建", "en-US": "Created"},
				Count:      20,
				Percentage: 20.0,
				Revenue:    protocol.Money{Amount: 200000, Currency: "USD"},
			},
		},
	}

	// 设置mock期望
	mockAnalyticsService.On("GetOrderAnalytics", ctx, mock.AnythingOfType("*protocol.OrderAnalyticsReq")).Return(mockResp, nil)

	// 执行测试
	req := &protocol.OrderAnalyticsReq{
		StartDate:   &startDate,
		EndDate:     &endDate,
		Granularity: "day",
	}

	resp, err := service.GetOrderAnalytics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, mockResp.Overview.TotalOrders, resp.Overview.TotalOrders)
	assert.Equal(t, mockResp.Overview.TotalRevenue.Amount, resp.Overview.TotalRevenue.Amount)
	assert.Len(t, resp.TrendData, 1)
	assert.Len(t, resp.StatusBreakdown, 1)

	// 验证mock调用
	mockAnalyticsService.AssertExpectations(t)
}

func TestOrderBIService_GetOrderMetrics_Overview(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 准备测试数据
	mockAnalyticsResp := &protocol.OrderAnalyticsResp{
		Overview: &protocol.OrderOverview{
			TotalOrders:  50,
			TotalRevenue: protocol.Money{Amount: 500000, Currency: "USD"},
		},
	}

	mockAnalyticsService.On("GetOrderAnalytics", ctx, mock.AnythingOfType("*protocol.OrderAnalyticsReq")).Return(mockAnalyticsResp, nil)

	// 执行测试
	req := &protocol.OrderMetricsReq{
		MetricType: "overview",
		StartDate:  &time.Time{},
		EndDate:    &time.Time{},
		Filters:    map[string]interface{}{"entityId": float64(123)},
	}

	resp, err := service.GetOrderMetrics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "overview", resp.MetricType)
	assert.NotNil(t, resp.Data)

	// 验证数据类型
	overview, ok := resp.Data.(*protocol.OrderOverview)
	assert.True(t, ok)
	assert.Equal(t, int64(50), overview.TotalOrders)

	mockAnalyticsService.AssertExpectations(t)
}

func TestOrderBIService_GetOrderMetrics_Trend(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 准备测试数据
	mockTrendData := []*protocol.OrderTrendData{
		{
			Date:       time.Now(),
			OrderCount: 20,
			Revenue:    protocol.Money{Amount: 200000, Currency: "USD"},
		},
	}

	mockAnalyticsResp := &protocol.OrderAnalyticsResp{
		TrendData: mockTrendData,
	}

	mockAnalyticsService.On("GetOrderAnalytics", ctx, mock.AnythingOfType("*protocol.OrderAnalyticsReq")).Return(mockAnalyticsResp, nil)

	// 执行测试
	req := &protocol.OrderMetricsReq{
		MetricType:  "trend",
		Granularity: "week",
	}

	resp, err := service.GetOrderMetrics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.Equal(t, "trend", resp.MetricType)
	
	trendData, ok := resp.Data.([]*protocol.OrderTrendData)
	assert.True(t, ok)
	assert.Len(t, trendData, 1)
	assert.Equal(t, int64(20), trendData[0].OrderCount)

	mockAnalyticsService.AssertExpectations(t)
}

func TestOrderBIService_GetOrderMetrics_Status(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 准备测试数据
	mockStatusData := []*protocol.OrderStatusStat{
		{
			Status:     5,
			StatusName: protocol.I18N{"zh-CN": "已完成", "en-US": "Completed"},
			Count:      30,
			Percentage: 60.0,
		},
	}

	mockAnalyticsResp := &protocol.OrderAnalyticsResp{
		StatusBreakdown: mockStatusData,
	}

	mockAnalyticsService.On("GetOrderAnalytics", ctx, mock.AnythingOfType("*protocol.OrderAnalyticsReq")).Return(mockAnalyticsResp, nil)

	// 执行测试
	req := &protocol.OrderMetricsReq{
		MetricType: "status",
	}

	resp, err := service.GetOrderMetrics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.Equal(t, "status", resp.MetricType)
	
	statusData, ok := resp.Data.([]*protocol.OrderStatusStat)
	assert.True(t, ok)
	assert.Len(t, statusData, 1)
	assert.Equal(t, int64(5), statusData[0].Status)
	assert.Equal(t, int64(30), statusData[0].Count)

	mockAnalyticsService.AssertExpectations(t)
}

func TestOrderBIService_GetOrderMetrics_Revenue(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 准备测试数据
	mockRevenueAnalysis := &protocol.RevenueAnalysis{
		TotalRevenue: protocol.Money{Amount: 1000000, Currency: "USD"},
		TotalProfit:  protocol.Money{Amount: 200000, Currency: "USD"},
		ProfitMargin: 20.0,
	}

	mockAnalyticsResp := &protocol.OrderAnalyticsResp{
		RevenueAnalysis: mockRevenueAnalysis,
	}

	mockAnalyticsService.On("GetOrderAnalytics", ctx, mock.AnythingOfType("*protocol.OrderAnalyticsReq")).Return(mockAnalyticsResp, nil)

	// 执行测试
	req := &protocol.OrderMetricsReq{
		MetricType: "revenue",
	}

	resp, err := service.GetOrderMetrics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.Equal(t, "revenue", resp.MetricType)
	
	revenueData, ok := resp.Data.(*protocol.RevenueAnalysis)
	assert.True(t, ok)
	assert.Equal(t, int64(1000000), revenueData.TotalRevenue.Amount)
	assert.Equal(t, float64(20.0), revenueData.ProfitMargin)

	mockAnalyticsService.AssertExpectations(t)
}

func TestOrderBIService_GetOrderMetrics_UnsupportedType(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 执行测试
	req := &protocol.OrderMetricsReq{
		MetricType: "invalid_type",
	}

	resp, err := service.GetOrderMetrics(ctx, req)

	// 断言
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "unsupported metric type")

	// 验证没有调用mock方法
	mockAnalyticsService.AssertNotCalled(t, "GetOrderAnalytics")
}

func TestOrderBIService_GetRealTimeMetrics(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 准备测试数据
	mockResp := &protocol.RealTimeMetricsResp{
		TodayOrders:      25,
		TodayRevenue:     protocol.Money{Amount: 250000, Currency: "USD"},
		ActiveBookings:   20,
		PendingOrders:    3,
		CompletionRate:   80.0,
		CancellationRate: 12.0,
		UpdateTime:       time.Now(),
	}

	mockAnalyticsService.On("GetRealTimeMetrics", ctx, mock.AnythingOfType("*protocol.RealTimeMetricsReq")).Return(mockResp, nil)

	// 执行测试
	req := &protocol.RealTimeMetricsReq{
		Metrics: []string{"todayOrders", "todayRevenue", "completionRate"},
	}

	resp, err := service.GetRealTimeMetrics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int64(25), resp.TodayOrders)
	assert.Equal(t, int64(250000), resp.TodayRevenue.Amount)
	assert.Equal(t, "USD", resp.TodayRevenue.Currency)
	assert.Equal(t, int64(20), resp.ActiveBookings)
	assert.Equal(t, int64(3), resp.PendingOrders)
	assert.Equal(t, float64(80.0), resp.CompletionRate)
	assert.Equal(t, float64(12.0), resp.CancellationRate)

	mockAnalyticsService.AssertExpectations(t)
}

func TestOrderBIService_ExportData(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	// 执行测试
	req := &protocol.ExportReq{
		ExportType: "excel",
		DataType:   "orders",
		StartDate:  &time.Time{},
		EndDate:    &time.Time{},
		Filters:    map[string]interface{}{"status": []int{1, 2, 3}},
		Fields:     []string{"id", "referenceNo", "status", "amount"},
	}

	resp, err := service.ExportData(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Contains(t, resp.FileName, "order_orders_")
	assert.Contains(t, resp.FileName, ".excel")
	assert.Contains(t, resp.FileUrl, "/api/bi/download/")
	assert.Greater(t, resp.FileSize, int64(0))
	assert.True(t, resp.ExpiryTime.After(time.Now()))

	// 验证没有调用analytics服务（导出是独立功能）
	mockAnalyticsService.AssertNotCalled(t, "GetOrderAnalytics")
}

func TestOrderBIService_GetOrderMetrics_DefaultDates(t *testing.T) {
	ctx := context.Background()
	mockAnalyticsService := new(MockOrderAnalyticsService)
	service := NewOrderBIService(mockAnalyticsService)

	mockAnalyticsResp := &protocol.OrderAnalyticsResp{
		Overview: &protocol.OrderOverview{
			TotalOrders: 100,
		},
	}

	// 验证默认日期的设置
	mockAnalyticsService.On("GetOrderAnalytics", ctx, mock.MatchedBy(func(req *protocol.OrderAnalyticsReq) bool {
		// 验证默认设置了开始日期（大约1个月前）和结束日期（现在）
		if req.StartDate == nil || req.EndDate == nil {
			return false
		}
		
		now := time.Now()
		oneMonthAgo := now.AddDate(0, -1, 0)
		
		// 允许一些时间误差（5分钟内）
		startTimeDiff := req.StartDate.Sub(oneMonthAgo)
		endTimeDiff := req.EndDate.Sub(now)
		
		return startTimeDiff.Abs() < 5*time.Minute && endTimeDiff.Abs() < 5*time.Minute
	})).Return(mockAnalyticsResp, nil)

	// 执行测试（不设置日期）
	req := &protocol.OrderMetricsReq{
		MetricType: "overview",
		// StartDate 和 EndDate 为 nil
	}

	resp, err := service.GetOrderMetrics(ctx, req)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	mockAnalyticsService.AssertExpectations(t)
}